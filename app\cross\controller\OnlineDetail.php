<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

declare (strict_types=1);

namespace app\cross\controller;

use app\base\BaseController;
use app\cross\model\CrossStoreSettleOnlineDetail;
use app\cross\validate\CrossStoreSettleOnlineDetailCheck;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\View;
use think\facade\Log;
use think\App;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class OnlineDetail extends BaseController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 网店订单跨店核销明细列表
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();

            // 构建查询条件
            $where = [];

            // 按月份筛选
            if (!empty($param['period'])) {
                $where[] = ['csod.period', '=', $param['period']];
            }

            // 按结算类型筛选
            if (!empty($param['settlement_type'])) {
                $where[] = ['csod.settlement_type', '=', $param['settlement_type']];
            }

            // 按核销门店筛选
            if (!empty($param['verification_store_ids']) && is_array($param['verification_store_ids'])) {
                $where[] = ['csod.verification_store_id', 'in', $param['verification_store_ids']];
            }

            // 按下单门店筛选
            if (!empty($param['order_store_ids']) && is_array($param['order_store_ids'])) {
                $where[] = ['csod.order_store_id', 'in', $param['order_store_ids']];
            }

            // 按客户归属门店筛选
            if (!empty($param['customer_belong_store_ids']) && is_array($param['customer_belong_store_ids'])) {
                $where[] = ['csod.customer_belong_store_id', 'in', $param['customer_belong_store_ids']];
            }

            // 按客户手机号筛选
            if (!empty($param['customer_mobile'])) {
                $where[] = ['csod.customer_mobile', 'like', '%' . $param['customer_mobile'] . '%'];
            }

            // 按客户姓名筛选
            if (!empty($param['customer_name'])) {
                $where[] = ['csod.customer_name', 'like', '%' . $param['customer_name'] . '%'];
            }

            // 按核销订单号筛选
            if (!empty($param['verification_order_number'])) {
                $where[] = ['csod.verification_order_number', 'like', '%' . $param['verification_order_number'] . '%'];
            }

            // 按购买订单号筛选
            if (!empty($param['purchase_order_number'])) {
                $where[] = ['csod.purchase_order_number', 'like', '%' . $param['purchase_order_number'] . '%'];
            }

            // 按结算时间范围筛选
            if (!empty($param['settlement_time_start'])) {
                $where[] = ['csod.settlement_time', '>=', $param['settlement_time_start']];
            }
            if (!empty($param['settlement_time_end'])) {
                $where[] = ['csod.settlement_time', '<=', $param['settlement_time_end'] . ' 23:59:59'];
            }

            // 分页参数
            $page = isset($param['page']) ? intval($param['page']) : 1;
            $limit = isset($param['limit']) ? intval($param['limit']) : 20;
            
            // 查询数据，支持分页
            $query = Db::name('CrossStoreSettleOnlineDetail')
                ->alias('csod')
                ->join('oa_department vs', 'vs.id = csod.verification_store_id', 'LEFT')
                ->join('oa_department os', 'os.id = csod.order_store_id', 'LEFT')
                ->join('oa_department cbs', 'cbs.id = csod.customer_belong_store_id', 'LEFT');

            // 获取总数
            $count = $query->where($where)->count();

            $list = $query->field('csod.*, vs.title as verification_store_name, os.title as order_store_name, 
                                   cbs.title as customer_belong_store_name')
                ->where($where)
                ->order('csod.settlement_time desc, csod.id desc')
                ->page($page, $limit)
                ->select()
                ->each(function ($item) {
                    // 格式化金额显示
                    $item['settlement_amount_formatted'] = number_format(floatval($item['settlement_amount']), 2);
                    $item['principal_payment_amount_formatted'] = number_format(floatval($item['principal_payment_amount']), 2);
                    $item['bonus_payment_amount_formatted'] = number_format(floatval($item['bonus_payment_amount']), 2);
                    $item['cash_payment_amount_formatted'] = number_format(floatval($item['cash_payment_amount']), 2);
                    $item['card_payment_amount_formatted'] = number_format(floatval($item['card_payment_amount']), 2);
                    
                    // 格式化时间
                    $item['settlement_time_formatted'] = !empty($item['settlement_time']) ? date('Y-m-d H:i:s', strtotime($item['settlement_time'])) : '';
                    $item['create_time_formatted'] = date('Y-m-d H:i:s', $item['create_time']);
                    $item['update_time_formatted'] = date('Y-m-d H:i:s', $item['update_time']);
                    
                    return $item;
                });

            // 计算合计数据（根据搜索条件变化，但不分页）
            $totalQuery = Db::name('CrossStoreSettleOnlineDetail')
                ->alias('csod')
                ->where($where);

            $totalData = $totalQuery->field([
                'SUM(settlement_amount) as total_settlement_amount',
                'SUM(principal_payment_amount) as total_principal_payment_amount',
                'SUM(bonus_payment_amount) as total_bonus_payment_amount',
                'SUM(cash_payment_amount) as total_cash_payment_amount',
                'SUM(card_payment_amount) as total_card_payment_amount',
                'SUM(product_quantity) as total_quantity',
                'COUNT(*) as total_count'
            ])->find();

            // 调试信息：记录查询结果
            Log::info('[跨店结算]-[网店订单跨店核销明细表]：合计查询调试', [
                'where_conditions' => $where,
                'total_data' => $totalData,
                'data_count' => count($list),
                'param' => $param
            ]);

            // 确保数据类型正确
            $totalSettlementAmount = floatval($totalData['total_settlement_amount'] ?? 0);
            $totalPrincipalPaymentAmount = floatval($totalData['total_principal_payment_amount'] ?? 0);
            $totalBonusPaymentAmount = floatval($totalData['total_bonus_payment_amount'] ?? 0);
            $totalCashPaymentAmount = floatval($totalData['total_cash_payment_amount'] ?? 0);
            $totalCardPaymentAmount = floatval($totalData['total_card_payment_amount'] ?? 0);
            $totalQuantity = intval($totalData['total_quantity'] ?? 0);

            $totalRowData = [
                'settlement_type' => '合计：',
                'verification_store_name' => '',
                'product_name' => '',
                'product_quantity' => $totalQuantity,
                'settlement_amount' => $totalSettlementAmount,
                'settlement_amount_formatted' => number_format($totalSettlementAmount, 2),
                'principal_payment_amount' => $totalPrincipalPaymentAmount,
                'principal_payment_amount_formatted' => number_format($totalPrincipalPaymentAmount, 2),
                'bonus_payment_amount' => $totalBonusPaymentAmount,
                'bonus_payment_amount_formatted' => number_format($totalBonusPaymentAmount, 2),
                'cash_payment_amount' => $totalCashPaymentAmount,
                'cash_payment_amount_formatted' => number_format($totalCashPaymentAmount, 2),
                'card_payment_amount' => $totalCardPaymentAmount,
                'card_payment_amount_formatted' => number_format($totalCardPaymentAmount, 2),
                'settlement_time_formatted' => '',
                'order_store_name' => '',
                'verification_order_number' => '',
                'purchase_order_number' => '',
                'customer_name' => '',
                'customer_mobile' => '',
                'customer_belong_store_name' => ''
            ];

            return [
                'code' => 0,
                'msg' => '',
                'count' => $count,
                'data' => $list,
                'totalRow' => $totalRowData
            ];
        } else {
            // 记录查看网店订单跨店核销明细列表页面日志
            add_log('view', 0, [], '[跨店结算]-[网店订单跨店核销明细表]：查看网店订单跨店核销明细列表页面');
            Log::info('[跨店结算]-[网店订单跨店核销明细表]：用户查看网店订单跨店核销明细列表页面，用户ID：' . $this->uid);
            return view();
        }
    }

    /**
     * 查看网店订单跨店核销明细详情（只读模式）
     */
    public function view()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;

        if (empty($id)) {
            return to_assign(1, '参数错误');
        }

        // 获取详情信息
        $detail = CrossStoreSettleOnlineDetail::getDetail($id);

        if (empty($detail)) {
            return to_assign(1, '数据不存在');
        }

        View::assign('detail', $detail);

        // 记录查看网店订单跨店核销明细详情日志
        add_log('view', $id, ['verification_order_number' => $detail['verification_order_number'], 'customer_name' => $detail['customer_name']], '[跨店结算]-[网店订单跨店核销明细表]：查看网店订单跨店核销明细详情');
        Log::info('[跨店结算]-[网店订单跨店核销明细表]：用户查看网店订单跨店核销明细详情，ID：' . $id . '，用户ID：' . $this->uid);

        return view();
    }

    /**
     * 编辑网店订单跨店核销明细信息
     */
    public function edit()
    {
        $param = get_params();

        if (request()->isPost()) {
            try {
                validate(CrossStoreSettleOnlineDetailCheck::class)->scene('edit')->check($param);
            } catch (ValidateException $e) {
                return to_assign(1, $e->getError());
            }

            $id = intval($param['id']);
            if (empty($id)) {
                return to_assign(1, '参数错误');
            }

            // 检查记录是否存在
            $existRecord = Db::name('CrossStoreSettleOnlineDetail')->where('id', $id)->find();
            if (empty($existRecord)) {
                return to_assign(1, '记录不存在');
            }

            try {
                // 准备更新数据
                $updateData = [
                    'settlement_type' => $param['settlement_type'],
                    'verification_store_id' => $param['verification_store_id'],
                    'product_name' => $param['product_name'],
                    'product_quantity' => $param['product_quantity'],
                    'settlement_amount' => $param['settlement_amount'],
                    'principal_payment_amount' => $param['principal_payment_amount'],
                    'bonus_payment_amount' => $param['bonus_payment_amount'],
                    'cash_payment_amount' => $param['cash_payment_amount'],
                    'card_payment_amount' => $param['card_payment_amount'],
                    'settlement_time' => !empty($param['settlement_time']) ? $param['settlement_time'] : null,
                    'order_store_id' => $param['order_store_id'],
                    'verification_order_number' => $param['verification_order_number'],
                    'purchase_order_number' => $param['purchase_order_number'],
                    'customer_name' => $param['customer_name'],
                    'customer_mobile' => $param['customer_mobile'],
                    'customer_belong_store_id' => $param['customer_belong_store_id'] ?? 0,
                    'period' => $param['period'] ?? '',
                    'update_time' => time()
                ];

                $res = Db::name('CrossStoreSettleOnlineDetail')->where('id', $id)->update($updateData);

                if ($res) {
                    // 记录操作日志
                    add_log('edit', $id, $param, '[跨店结算]-[网店订单跨店核销明细表]：编辑网店订单跨店核销明细信息');
                    Log::info('[跨店结算]-[网店订单跨店核销明细表]：编辑网店订单跨店核销明细信息成功，ID：' . $id . '，用户ID：' . $this->uid);
                    return to_assign(0, '保存成功');
                } else {
                    return to_assign(1, '保存失败');
                }
            } catch (\think\exception\HttpResponseException $e) {
                // 重新抛出HttpResponseException，这是to_assign函数正常的响应机制
                throw $e;
            } catch (\Exception $e) {
                Log::error('[跨店结算]-[网店订单跨店核销明细表]：编辑网店订单跨店核销明细信息失败，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
                return to_assign(1, '保存失败：' . $e->getMessage());
            }
        } else {
            // GET请求，显示编辑表单
            $id = isset($param['id']) ? $param['id'] : 0;
            if (empty($id)) {
                return to_assign(1, '参数错误');
            }

            $detail = CrossStoreSettleOnlineDetail::getDetail($id);
            if (empty($detail)) {
                return to_assign(1, '数据不存在');
            }

            // 获取门店列表（只包含门店）
            $storeList = Db::name('Department')
                ->where('status', 1)
                ->where('remark', '门店')
                ->select()
                ->toArray();
            
            View::assign('detail', $detail);
            View::assign('store_list', $storeList);

            return view();
        }
    }

    /**
     * 导出网店订单跨店核销明细数据
     */
    public function export()
    {
        $param = get_params();

        // 构建查询条件（复用index方法的逻辑）
        $where = [];

        // 按月份筛选
        if (!empty($param['period'])) {
            $where[] = ['csod.period', '=', $param['period']];
        }

        // 按结算类型筛选
        if (!empty($param['settlement_type'])) {
            $where[] = ['csod.settlement_type', '=', $param['settlement_type']];
        }

        // 按核销门店筛选
        if (!empty($param['verification_store_ids']) && is_array($param['verification_store_ids'])) {
            $where[] = ['csod.verification_store_id', 'in', $param['verification_store_ids']];
        }

        // 按下单门店筛选
        if (!empty($param['order_store_ids']) && is_array($param['order_store_ids'])) {
            $where[] = ['csod.order_store_id', 'in', $param['order_store_ids']];
        }

        // 按客户归属门店筛选
        if (!empty($param['customer_belong_store_ids']) && is_array($param['customer_belong_store_ids'])) {
            $where[] = ['csod.customer_belong_store_id', 'in', $param['customer_belong_store_ids']];
        }

        // 按客户手机号筛选
        if (!empty($param['customer_mobile'])) {
            $where[] = ['csod.customer_mobile', 'like', '%' . $param['customer_mobile'] . '%'];
        }

        // 按客户姓名筛选
        if (!empty($param['customer_name'])) {
            $where[] = ['csod.customer_name', 'like', '%' . $param['customer_name'] . '%'];
        }

        // 按核销订单号筛选
        if (!empty($param['verification_order_number'])) {
            $where[] = ['csod.verification_order_number', 'like', '%' . $param['verification_order_number'] . '%'];
        }

        // 按购买订单号筛选
        if (!empty($param['purchase_order_number'])) {
            $where[] = ['csod.purchase_order_number', 'like', '%' . $param['purchase_order_number'] . '%'];
        }

        // 按结算时间范围筛选
        if (!empty($param['settlement_time_start'])) {
            $where[] = ['csod.settlement_time', '>=', $param['settlement_time_start']];
        }
        if (!empty($param['settlement_time_end'])) {
            $where[] = ['csod.settlement_time', '<=', $param['settlement_time_end'] . ' 23:59:59'];
        }

        // 查询数据
        $query = Db::name('CrossStoreSettleOnlineDetail')
            ->alias('csod')
            ->join('oa_department vs', 'vs.id = csod.verification_store_id', 'LEFT')
            ->join('oa_department os', 'os.id = csod.order_store_id', 'LEFT')
            ->join('oa_department cbs', 'cbs.id = csod.customer_belong_store_id', 'LEFT');

        $list = $query->field('csod.*, vs.title as verification_store_name, os.title as order_store_name, 
                               cbs.title as customer_belong_store_name')
            ->where($where)
            ->order('csod.settlement_time desc, csod.id desc')
            ->select()
            ->toArray();

        // 创建Excel文件
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // 设置表头
        $headers = [
            'A1' => '结算类型',
            'B1' => '核销门店',
            'C1' => '商品名称',
            'D1' => '商品数量',
            'E1' => '结算金额',
            'F1' => '本金支付金额',
            'G1' => '赠金支付金额',
            'H1' => '现金类支付金额',
            'I1' => '次卡支付金额',
            'J1' => '结算时间',
            'K1' => '下单门店',
            'L1' => '核销订单号',
            'M1' => '购买订单号',
            'N1' => '客户姓名',
            'O1' => '客户手机号',
            'P1' => '归属门店'
        ];

        foreach ($headers as $cell => $header) {
            $sheet->setCellValue($cell, $header);
        }

        // 设置表头样式
        $headerStyle = [
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'E6E6E6']
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN
                ]
            ]
        ];
        $sheet->getStyle('A1:P1')->applyFromArray($headerStyle);

        // 填充数据
        $row = 2;
        foreach ($list as $item) {
            $sheet->setCellValue('A' . $row, $item['settlement_type']);
            $sheet->setCellValue('B' . $row, $item['verification_store_name']);
            $sheet->setCellValue('C' . $row, $item['product_name']);
            $sheet->setCellValue('D' . $row, $item['product_quantity']);
            $sheet->setCellValue('E' . $row, floatval($item['settlement_amount']));
            $sheet->setCellValue('F' . $row, floatval($item['principal_payment_amount']));
            $sheet->setCellValue('G' . $row, floatval($item['bonus_payment_amount']));
            $sheet->setCellValue('H' . $row, floatval($item['cash_payment_amount']));
            $sheet->setCellValue('I' . $row, floatval($item['card_payment_amount']));
            $sheet->setCellValue('J' . $row, !empty($item['settlement_time']) ? date('Y-m-d H:i:s', strtotime($item['settlement_time'])) : '');
            $sheet->setCellValue('K' . $row, $item['order_store_name']);
            $sheet->setCellValue('L' . $row, $item['verification_order_number']);
            $sheet->setCellValue('M' . $row, $item['purchase_order_number']);
            $sheet->setCellValue('N' . $row, $item['customer_name']);
            $sheet->setCellValue('O' . $row, $item['customer_mobile']);
            $sheet->setCellValue('P' . $row, $item['customer_belong_store_name']);
            $row++;
        }

        // 设置列宽
        $sheet->getColumnDimension('A')->setWidth(20);
        $sheet->getColumnDimension('B')->setWidth(15);
        $sheet->getColumnDimension('C')->setWidth(20);
        $sheet->getColumnDimension('D')->setWidth(8);
        $sheet->getColumnDimension('E')->setWidth(12);
        $sheet->getColumnDimension('F')->setWidth(15);
        $sheet->getColumnDimension('G')->setWidth(15);
        $sheet->getColumnDimension('H')->setWidth(15);
        $sheet->getColumnDimension('I')->setWidth(15);
        $sheet->getColumnDimension('J')->setWidth(18);
        $sheet->getColumnDimension('K')->setWidth(15);
        $sheet->getColumnDimension('L')->setWidth(20);
        $sheet->getColumnDimension('M')->setWidth(20);
        $sheet->getColumnDimension('N')->setWidth(12);
        $sheet->getColumnDimension('O')->setWidth(15);
        $sheet->getColumnDimension('P')->setWidth(15);

        // 设置数字格式
        $sheet->getStyle('E2:E' . ($row - 1))->getNumberFormat()->setFormatCode('#,##0.00');
        $sheet->getStyle('F2:F' . ($row - 1))->getNumberFormat()->setFormatCode('#,##0.00');
        $sheet->getStyle('G2:G' . ($row - 1))->getNumberFormat()->setFormatCode('#,##0.00');
        $sheet->getStyle('H2:H' . ($row - 1))->getNumberFormat()->setFormatCode('#,##0.00');
        $sheet->getStyle('I2:I' . ($row - 1))->getNumberFormat()->setFormatCode('#,##0.00');

        // 生成文件名
        $filename = '网店订单跨店核销明细表_' . date('YmdHis') . '.xlsx';

        // 设置HTTP头
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        // 输出文件
        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');

        // 记录导出日志
        add_log('export', 0, $param, '[跨店结算]-[网店订单跨店核销明细表]：导出网店订单跨店核销明细数据');
        Log::info('[跨店结算]-[网店订单跨店核销明细表]：用户导出网店订单跨店核销明细数据，条件：' . json_encode($param) . '，用户ID：' . $this->uid);

        exit;
    }
}