<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

declare (strict_types=1);

namespace app\cross\controller;

use app\base\BaseController;
use app\cross\model\CrossStoreSettleCardDetail;
use app\cross\validate\CrossStoreSettleCardDetailCheck;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\View;
use think\facade\Log;
use think\App;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class CardDetail extends BaseController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 次卡跨店结算明细列表
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();

            // 构建查询条件
            $where = [];

            // 按月份筛选
            if (!empty($param['period'])) {
                $where[] = ['cscd.period', '=', $param['period']];
            }

            // 按结算类型筛选
            if (!empty($param['settlement_type'])) {
                $where[] = ['cscd.settlement_type', '=', $param['settlement_type']];
            }

            // 按消费门店筛选
            if (!empty($param['consume_store_ids']) && is_array($param['consume_store_ids'])) {
                $where[] = ['cscd.consume_store_id', 'in', $param['consume_store_ids']];
            }

            // 按开卡门店筛选
            if (!empty($param['card_open_store_ids']) && is_array($param['card_open_store_ids'])) {
                $where[] = ['cscd.card_open_store_id', 'in', $param['card_open_store_ids']];
            }

            // 按客户归属门店筛选
            if (!empty($param['customer_belong_store_ids']) && is_array($param['customer_belong_store_ids'])) {
                $where[] = ['cscd.customer_belong_store_id', 'in', $param['customer_belong_store_ids']];
            }

            // 按客户手机号筛选
            if (!empty($param['customer_mobile'])) {
                $where[] = ['cscd.customer_mobile', 'like', '%' . $param['customer_mobile'] . '%'];
            }

            // 按客户姓名筛选
            if (!empty($param['customer_name'])) {
                $where[] = ['cscd.customer_name', 'like', '%' . $param['customer_name'] . '%'];
            }

            // 按订单号筛选
            if (!empty($param['order_number'])) {
                $where[] = ['cscd.order_number', 'like', '%' . $param['order_number'] . '%'];
            }

            // 按结算时间范围筛选
            if (!empty($param['settlement_time_start'])) {
                $where[] = ['cscd.settlement_time', '>=', $param['settlement_time_start']];
            }
            if (!empty($param['settlement_time_end'])) {
                $where[] = ['cscd.settlement_time', '<=', $param['settlement_time_end'] . ' 23:59:59'];
            }

            // 获取分页参数
            $page = isset($param['page']) ? intval($param['page']) : 1;
            $limit = isset($param['limit']) ? intval($param['limit']) : 20;

            // 查询分页数据
            $query = Db::name('cross_store_settle_card_detail')
                ->alias('cscd')
                ->join('oa_department cs', 'cs.id = cscd.consume_store_id', 'LEFT')
                ->join('oa_department cos', 'cos.id = cscd.card_open_store_id', 'LEFT')
                ->join('oa_department cbs', 'cbs.id = cscd.customer_belong_store_id', 'LEFT');

            // 先获取总记录数
            $totalCount = $query->where($where)->count();

            // 查询当前页数据
            $list = $query->field('cscd.*, cs.title as consume_store_name,
                                   cos.title as card_open_store_name, cbs.title as customer_belong_store_name')
                ->where($where)
                ->order('cscd.settlement_time desc, cscd.id desc')
                ->page($page, $limit)
                ->select()
                ->each(function ($item) {
                    // 格式化金额显示
                    $item['settlement_amount_formatted'] = number_format(floatval($item['settlement_amount']), 2);

                    // 格式化时间
                    $item['settlement_time_formatted'] = !empty($item['settlement_time']) ? date('Y-m-d H:i:s', strtotime($item['settlement_time'])) : '';
                    $item['create_time_formatted'] = date('Y-m-d H:i:s', $item['create_time']);
                    $item['update_time_formatted'] = date('Y-m-d H:i:s', $item['update_time']);

                    return $item;
                });

            // 计算合计数据（根据搜索条件变化，但不分页）
            $totalQuery = Db::name('cross_store_settle_card_detail')
                ->alias('cscd')
                ->where($where);

            $totalData = $totalQuery->field([
                'SUM(settlement_amount) as total_settlement_amount',
                'SUM(product_quantity) as total_quantity',
                'COUNT(*) as total_count'
            ])->find();

            // 调试信息：记录查询结果
            Log::info('[跨店结算]-[次卡跨店结算明细表]：分页查询调试', [
                'where_conditions' => $where,
                'total_data' => $totalData,
                'total_count' => $totalCount,
                'current_page_count' => count($list),
                'page' => $page,
                'limit' => $limit,
                'param' => $param
            ]);

            // 确保数据类型正确
            $totalSettlementAmount = floatval($totalData['total_settlement_amount'] ?? 0);
            $totalQuantity = intval($totalData['total_quantity'] ?? 0);

            $totalRowData = [
                'settlement_type' => '合计：',
                'consume_store_name' => '',
                'product_name' => '',
                'product_quantity' => $totalQuantity,
                'card_name' => '',
                'settlement_amount' => $totalSettlementAmount,
                'settlement_amount_formatted' => number_format($totalSettlementAmount, 2),
                'settlement_time_formatted' => '',
                'order_number' => '',
                'related_order_number' => '',
                'card_open_store_name' => '',
                'customer_name' => '',
                'customer_mobile' => '',
                'customer_belong_store_name' => ''
            ];

            return [
                'code' => 0,
                'msg' => '',
                'count' => $totalCount,  // 使用总记录数，支持分页
                'data' => $list,
                'totalRow' => $totalRowData
            ];
        } else {
            // 记录查看次卡跨店结算明细列表页面日志
            add_log('view', 0, [], '[跨店结算]-[次卡跨店结算明细表]：查看次卡跨店结算明细列表页面');
            Log::info('[跨店结算]-[次卡跨店结算明细表]：用户查看次卡跨店结算明细列表页面，用户ID：' . $this->uid);
            return view();
        }
    }

    /**
     * 查看次卡跨店结算明细详情（只读模式）
     */
    public function view()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;

        if (empty($id)) {
            return to_assign(1, '参数错误');
        }

        // 获取详情信息
        $detail = CrossStoreSettleCardDetail::getDetail($id);

        if (empty($detail)) {
            return to_assign(1, '数据不存在');
        }

        View::assign('detail', $detail);

        // 记录查看次卡跨店结算明细详情日志
        add_log('view', $id, ['order_number' => $detail['order_number'], 'customer_name' => $detail['customer_name']], '[跨店结算]-[次卡跨店结算明细表]：查看次卡跨店结算明细详情');
        Log::info('[跨店结算]-[次卡跨店结算明细表]：用户查看次卡跨店结算明细详情，ID：' . $id . '，用户ID：' . $this->uid);

        return view();
    }

    /**
     * 编辑次卡跨店结算明细信息
     */
    public function edit()
    {
        $param = get_params();

        if (request()->isPost()) {
            try {
                validate(CrossStoreSettleCardDetailCheck::class)->scene('edit')->check($param);
            } catch (ValidateException $e) {
                return to_assign(1, $e->getError());
            }

            $id = intval($param['id']);
            if (empty($id)) {
                return to_assign(1, '参数错误');
            }

            // 检查记录是否存在
            $existRecord = Db::name('cross_store_settle_card_detail')->where('id', $id)->find();
            if (empty($existRecord)) {
                return to_assign(1, '记录不存在');
            }

            try {
                // 准备更新数据
                $updateData = [
                    'settlement_type' => $param['settlement_type'],
                    'consume_store_id' => $param['consume_store_id'],
                    'product_name' => $param['product_name'],
                    'product_quantity' => $param['product_quantity'],
                    'card_name' => $param['card_name'],
                    'settlement_amount' => $param['settlement_amount'],
                    'settlement_time' => !empty($param['settlement_time']) ? $param['settlement_time'] : null,
                    'card_open_store_id' => $param['card_open_store_id'] ?? 0,
                    'order_number' => $param['order_number'],
                    'related_order_number' => $param['related_order_number'] ?? '',
                    'customer_name' => $param['customer_name'],
                    'customer_mobile' => $param['customer_mobile'],
                    'customer_belong_store_id' => $param['customer_belong_store_id'] ?? 0,
                    'period' => $param['period'] ?? '',
                    'update_time' => time()
                ];

                $res = Db::name('cross_store_settle_card_detail')->where('id', $id)->update($updateData);

                if ($res) {
                    // 记录操作日志
                    add_log('edit', $id, $param, '[跨店结算]-[次卡跨店结算明细表]：编辑次卡跨店结算明细信息');
                    Log::info('[跨店结算]-[次卡跨店结算明细表]：编辑次卡跨店结算明细信息成功，ID：' . $id . '，用户ID：' . $this->uid);
                    return to_assign(0, '保存成功');
                } else {
                    return to_assign(1, '保存失败');
                }
            } catch (\think\exception\HttpResponseException $e) {
                // 重新抛出HttpResponseException，这是to_assign函数正常的响应机制
                throw $e;
            } catch (\Exception $e) {
                Log::error('[跨店结算]-[次卡跨店结算明细表]：编辑次卡跨店结算明细信息失败，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
                return to_assign(1, '保存失败：' . $e->getMessage());
            }
        } else {
            // GET请求，显示编辑表单
            $id = isset($param['id']) ? $param['id'] : 0;
            if (empty($id)) {
                return to_assign(1, '参数错误');
            }

            $detail = CrossStoreSettleCardDetail::getDetail($id);
            if (empty($detail)) {
                return to_assign(1, '数据不存在');
            }

            // 获取门店列表（只包含门店）
            $storeList = Db::name('Department')
                ->where('status', 1)
                ->where('remark', '门店')
                ->select()
                ->toArray();
            
            View::assign('detail', $detail);
            View::assign('store_list', $storeList);

            return view();
        }
    }

    /**
     * 导出次卡跨店结算明细数据
     */
    public function export()
    {
        $param = get_params();

        // 构建查询条件（与index方法相同的逻辑）
        $where = [];

        // 按月份筛选
        if (!empty($param['period'])) {
            $where[] = ['cscd.period', '=', $param['period']];
        }

        // 按结算类型筛选
        if (!empty($param['settlement_type'])) {
            $where[] = ['cscd.settlement_type', '=', $param['settlement_type']];
        }

        // 按消费门店筛选
        if (!empty($param['consume_store_ids']) && is_array($param['consume_store_ids'])) {
            $where[] = ['cscd.consume_store_id', 'in', $param['consume_store_ids']];
        }

        // 按开卡门店筛选
        if (!empty($param['card_open_store_ids']) && is_array($param['card_open_store_ids'])) {
            $where[] = ['cscd.card_open_store_id', 'in', $param['card_open_store_ids']];
        }

        // 按客户归属门店筛选
        if (!empty($param['customer_belong_store_ids']) && is_array($param['customer_belong_store_ids'])) {
            $where[] = ['cscd.customer_belong_store_id', 'in', $param['customer_belong_store_ids']];
        }

        // 按客户手机号筛选
        if (!empty($param['customer_mobile'])) {
            $where[] = ['cscd.customer_mobile', 'like', '%' . $param['customer_mobile'] . '%'];
        }

        // 按客户姓名筛选
        if (!empty($param['customer_name'])) {
            $where[] = ['cscd.customer_name', 'like', '%' . $param['customer_name'] . '%'];
        }

        // 按订单号筛选
        if (!empty($param['order_number'])) {
            $where[] = ['cscd.order_number', 'like', '%' . $param['order_number'] . '%'];
        }

        // 查询数据
        $list = Db::name('cross_store_settle_card_detail')
            ->alias('cscd')
            ->join('oa_department cs', 'cs.id = cscd.consume_store_id', 'LEFT')
            ->join('oa_department cos', 'cos.id = cscd.card_open_store_id', 'LEFT')
            ->join('oa_department cbs', 'cbs.id = cscd.customer_belong_store_id', 'LEFT')
            ->field('cscd.*, cs.title as consume_store_name,
                     cos.title as card_open_store_name, cbs.title as customer_belong_store_name')
            ->where($where)
            ->order('cscd.settlement_time desc, cscd.id desc')
            ->select()
            ->toArray();

        // 创建Excel文件
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // 设置表头
        $headers = [
            'A1' => '结算类型',
            'B1' => '消费门店',
            'C1' => '商品名称',
            'D1' => '数量',
            'E1' => '次卡名称',
            'F1' => '结算金额',
            'G1' => '结算时间',
            'H1' => '开卡门店',
            'I1' => '发生订单号',
            'J1' => '关联订单号',
            'K1' => '客户姓名',
            'L1' => '客户手机号',
            'M1' => '客户归属门店',
            'N1' => '快照月份'
        ];

        foreach ($headers as $cell => $value) {
            $sheet->setCellValue($cell, $value);
        }

        // 填充数据
        $row = 2;
        foreach ($list as $item) {
            $sheet->setCellValue('A' . $row, $item['settlement_type']);
            $sheet->setCellValue('B' . $row, $item['consume_store_name']);
            $sheet->setCellValue('C' . $row, $item['product_name']);
            $sheet->setCellValue('D' . $row, $item['product_quantity']);
            $sheet->setCellValue('E' . $row, $item['card_name']);
            $sheet->setCellValue('F' . $row, $item['settlement_amount']);
            $sheet->setCellValue('G' . $row, $item['settlement_time']);
            $sheet->setCellValue('H' . $row, $item['card_open_store_name']);
            $sheet->setCellValue('I' . $row, $item['order_number']);
            $sheet->setCellValue('J' . $row, $item['related_order_number']);
            $sheet->setCellValue('K' . $row, $item['customer_name']);
            $sheet->setCellValue('L' . $row, $item['customer_mobile']);
            $sheet->setCellValue('M' . $row, $item['customer_belong_store_name']);
            $sheet->setCellValue('N' . $row, $item['period']);
            $row++;
        }

        // 设置文件名
        $filename = '次卡跨店结算明细_' . date('YmdHis') . '.xlsx';

        // 输出Excel文件
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');

        // 记录导出日志
        add_log('export', 0, $param, '[跨店结算]-[次卡跨店结算明细表]：导出次卡跨店结算明细数据');
        Log::info('[跨店结算]-[次卡跨店结算明细表]：导出次卡跨店结算明细数据，用户ID：' . $this->uid);

        exit;
    }
}
